export interface ChartDataPoint {
  x: Date | number | string;
  y: number;
  [key: string]: any;
}

export interface ChartDataset {
  label: string;
  color: string;
  data: ChartDataPoint[];
  [key: string]: any;
}

export interface ChartTransformOptions {
  maxDataPoints?: number;
  sortByTime?: boolean;
  filterInvalid?: boolean;
  groupBy?: string;
}

/**
 * Predefined color palettes for charts
 */
export const CHART_COLOR_PALETTES = {
  default: [
    '#3B82F6',
    '#EF4444',
    '#10B981',
    '#F59E0B',
    '#8B5CF6',
    '#EC4899',
    '#06B6D4',
    '#84CC16',
    '#F97316',
    '#6366F1',
  ],
  cybersecurity: [
    '#dc2626', // red for critical
    '#f59e0b', // amber for high
    '#eab308', // yellow for medium
    '#22c55e', // green for low
    '#3b82f6', // blue for info
  ],
  status: {
    success: '#22c55e',
    warning: '#f59e0b',
    danger: '#dc2626',
    info: '#3b82f6',
    secondary: '#6b7280',
  },
} as const;

/**
 * Gets an array of colors for charts based on count needed
 */
export function getChartColors(
  count: number,
  palette: 'default' | 'cybersecurity' = 'default',
): string[] {
  const colors = CHART_COLOR_PALETTES[palette];
  const result: string[] = [];

  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }

  return result;
}

/**
 * Gets color for status/severity levels
 */
export function getStatusColor(status: string): string {
  const normalized = status.toLowerCase();

  switch (normalized) {
    case 'critical':
    case 'high':
    case 'danger':
    case 'error':
      return CHART_COLOR_PALETTES.status.danger;

    case 'warning':
    case 'medium':
    case 'warn':
      return CHART_COLOR_PALETTES.status.warning;

    case 'success':
    case 'low':
    case 'ok':
    case 'healthy':
      return CHART_COLOR_PALETTES.status.success;

    case 'info':
    case 'information':
      return CHART_COLOR_PALETTES.status.info;

    default:
      return CHART_COLOR_PALETTES.status.secondary;
  }
}



/**
 * Transforms raw data into chart-ready format
 */
export function transformForChart<T extends Record<string, any>>(
  data: T[],
  options: ChartTransformOptions = {},
): ChartDataset[] {
  const {
    maxDataPoints = 50,
    sortByTime = true,
    filterInvalid = true,
    groupBy,
  } = options;

  if (!data.length) return [];

  // Group data if groupBy is specified
  if (groupBy) {
    return transformGroupedData(data, groupBy, options);
  }

  // Simple transformation for single dataset
  const colors = getChartColors(1);

  const chartData = data
    .map((item, index) => ({
      x: item.timestamp ? new Date(item.timestamp) : new Date(),
      y: typeof item.value === 'number' ? item.value : 0,
      ...item,
    }))
    .filter((entry) => {
      if (!filterInvalid) return true;
      return !isNaN(entry.x.getTime()) && !isNaN(entry.y);
    });

  if (sortByTime) {
    chartData.sort((a, b) => a.x.getTime() - b.x.getTime());
  }

  // Limit data points
  const limitedData = chartData.slice(-maxDataPoints);

  return [
    {
      label: 'Data',
      color: colors[0],
      data: limitedData,
    },
  ];
}

/**
 * Transforms grouped data into multiple chart datasets
 */
function transformGroupedData<T extends Record<string, any>>(
  data: T[],
  groupBy: string,
  options: ChartTransformOptions,
): ChartDataset[] {
  const {
    maxDataPoints = 50,
    sortByTime = true,
    filterInvalid = true,
  } = options;

  // Group data by the specified field
  const groups = data.reduce(
    (acc, item) => {
      const groupKey = item[groupBy] || 'unknown';
      if (!acc[groupKey]) {
        acc[groupKey] = [];
      }
      acc[groupKey].push(item);
      return acc;
    },
    {} as Record<string, T[]>,
  );

  const colors = getChartColors(Object.keys(groups).length);

  return Object.entries(groups)
    .map(([groupKey, groupData], index) => {
      const chartData = groupData
        .map((item) => ({
          x: item.timestamp ? new Date(item.timestamp) : new Date(),
          y: typeof item.value === 'number' ? item.value : 0,
          ...item,
        }))
        .filter((entry) => {
          if (!filterInvalid) return true;
          return !isNaN(entry.x.getTime()) && !isNaN(entry.y);
        });

      if (sortByTime) {
        chartData.sort((a, b) => a.x.getTime() - b.x.getTime());
      }

      // Limit data points
      const limitedData = chartData.slice(-maxDataPoints);

      return {
        label: groupKey,
        color: colors[index],
        data: limitedData,
      };
    })
    .filter((dataset) => dataset.data.length > 0);
}

/**
 * Transforms deployment data specifically (matching the settings page pattern)
 */
export function transformDeploymentData(
  deployments: Array<{
    name: string;
    namespace: string;
    data: Record<string, any>;
  }>,
): Record<string, ChartDataset[]> {
  if (!deployments.length) return {};

  const colors = getChartColors(20);

  // Group deployments by namespace
  const namespaceGroups = deployments.reduce(
    (groups, deployment) => {
      if (!groups[deployment.namespace]) {
        groups[deployment.namespace] = [];
      }
      groups[deployment.namespace].push(deployment);
      return groups;
    },
    {} as Record<string, typeof deployments>,
  );

  // Transform each namespace group into chart data
  return Object.entries(namespaceGroups).reduce(
    (result, [namespace, namespaceDeployments]) => {
      let colorIdx = 0;
      const chartData: ChartDataset[] = [];
      namespaceDeployments.forEach((deployment) => {

        const timeseries = deployment.data?.timeseries || deployment.data;
        if (timeseries && typeof timeseries === 'object') {
          const dataEntries = Object.entries(timeseries)
            .map(([timestamp, value]) => {
              const time = parseInt(timestamp);
              return {
                x: new Date(time),
                y: typeof value === 'number' ? value : 0,
              };
            })
            .filter((entry) => !isNaN(entry.x.getTime()) && !isNaN(entry.y))
            .sort((a, b) => a.x.getTime() - b.x.getTime());
          const limitedData = dataEntries.slice(-60);
          if (limitedData.length > 0) {
            chartData.push({
              label: deployment.name,
              color: colors[colorIdx % colors.length],
              data: limitedData,
            });
            colorIdx++;
          }
        }

        const predictions = deployment.data?.predictions;
        const pred1min = predictions && predictions['1min'];
        if (pred1min && typeof pred1min === 'object') {
          const predEntries = Object.entries(pred1min)
            .map(([timestamp, value]) => {
              const time = parseInt(timestamp);
              return {
                x: new Date(time),
                y: typeof value === 'number' ? value : 0,
              };
            })
            .filter((entry) => !isNaN(entry.x.getTime()) && !isNaN(entry.y))
            .sort((a, b) => a.x.getTime() - b.x.getTime());
          const limitedPred = predEntries.slice(-60);
          if (limitedPred.length > 0) {
            chartData.push({
              label: deployment.name + ' (1min prediction)',
              color: colors[colorIdx % colors.length],
              data: limitedPred,
            });
            colorIdx++;
          }
        }
      });
      if (chartData.length > 0) {
        result[namespace] = chartData;
      }
      return result;
    },
    {} as Record<string, ChartDataset[]>,
  );
}